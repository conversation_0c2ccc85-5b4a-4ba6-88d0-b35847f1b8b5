# 合同协议新增/编辑页面接口联调测试说明

## 已完成的接口适配工作

### 1. 字段名称映射
- ✅ `contractCode` → `contractNo` (合同编码)
- ✅ `contractType` → `contractTypeName` (合同协议类型)
- ✅ `file` → `docList` (附件)
- ✅ 投建协议表格字段映射到 `toujianxieyiList`

### 2. 新增字段
- ✅ `submitFlag` - 提交标志(0:保存 1:提交)
- ✅ `attribute` - 属性
- ✅ `agreementType` - 协议类型
- ✅ `businessPolicy` - 商务政策
- ✅ `contractAmountOrRatio` - 合同金额/比例
- ✅ `partA`, `partB`, `partC`等 - 合同协议方字段

### 3. 数据格式转换
- ✅ 文件上传数据转换为 `docList` 格式
- ✅ 投建协议数据格式化（数字精度、布尔值）
- ✅ 日期格式统一为 YYYY-MM-DD
- ✅ 合同协议方动态字段映射

### 4. API接口更新
- ✅ 更新保存接口URL为 `/contractInfo/save`
- ✅ 更新详情接口URL为 `/contractInfo/basicDetail`
- ✅ 添加运管申请单号信息接口
- ✅ 添加关联项目信息接口

### 5. 验证和错误处理
- ✅ 表单验证规则完善
- ✅ 合同协议方必填验证
- ✅ 日期逻辑验证（生效时间 < 失效时间）
- ✅ 接口调用错误处理
- ✅ 用户友好的错误提示

## 测试要点

### 1. 新增功能测试
1. 填写基本信息（必填项验证）
2. 选择合同协议类型（触发其他信息显示）
3. 添加合同协议方（最少一个，最多十个）
4. 上传附件（格式和大小验证）
5. 填写投建协议详情表格
6. 保存和提交功能

### 2. 编辑功能测试
1. 数据回显正确性
2. 文件数据回显
3. 合同协议方回显
4. 投建协议表格数据回显
5. 修改后保存

### 3. 数据格式验证
1. 检查提交数据的字段名是否与接口文档一致
2. 验证数字字段精度（租赁费比例1位小数，计费标准4位小数）
3. 验证布尔值字段格式（Y/N）
4. 验证日期格式（YYYY-MM-DD）

### 4. 错误处理测试
1. 网络错误处理
2. 服务器错误处理
3. 表单验证错误提示
4. 业务逻辑错误提示

## 接口数据格式示例

### 保存请求数据格式
```json
{
  "contractNo": "HT2024001",
  "contractName": "测试合同",
  "contractTypeName": "投建协议申请记录-充电",
  "businessType": "充电桩建设",
  "attribute": "新建",
  "agreementType": "投建协议",
  "businessPolicy": "分润比例30%",
  "contractAmountOrRatio": "100万元",
  "effectiveTime": "2024-01-01",
  "expireTime": "2024-12-31",
  "omApplyNo": "YG2024001",
  "partA": "甲方公司",
  "partB": "乙方公司",
  "submitFlag": 1,
  "docList": [
    {
      "docName": "合同文件.pdf",
      "storePath": "/files/contract.pdf",
      "storeType": "0",
      "docType": "1",
      "docClassify": "contract",
      "directory": "contract",
      "delFlag": "0",
      "businessType": "充电桩建设",
      "relaBizId": 0
    }
  ],
  "toujianxieyiList": [
    {
      "stationName": "测试站点",
      "stationCode": "ST001",
      "region": "华东区",
      "rentalFeeOrSplitRatio": 30.0,
      "meterOwner": "电力公司",
      "billingMethod": "峰谷电价",
      "billingStandard": 0.8500,
      "hasLateFee": "Y",
      "revenueModel": "分润模式",
      "pileCount": 10,
      "gunCount": 20,
      "contractId": 0
    }
  ]
}
```

## 注意事项

1. **字段映射**：确保前端字段名与后端API字段名完全一致
2. **数据类型**：注意数字字段的精度要求
3. **必填验证**：按照接口文档要求设置必填项
4. **文件上传**：确保文件数据格式符合后端要求
5. **错误处理**：提供用户友好的错误提示信息

## 后续优化建议

1. 添加更多的业务逻辑验证
2. 优化用户体验（加载状态、进度提示）
3. 添加数据缓存机制
4. 完善国际化支持
5. 添加单元测试覆盖
