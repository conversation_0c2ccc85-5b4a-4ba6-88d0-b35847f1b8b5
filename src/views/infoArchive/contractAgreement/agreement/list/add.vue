<!-- 新增/编辑合同协议 -->
<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>{{ type === "add" ? "新增" : "编辑" }}合同协议</h3>

    <!-- 基本信息 -->
    <el-card id="baseInfo">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="baseParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
      >
        <!-- 运管申请单号自定义插槽 -->
        <template #omApplyNo>
          <el-autocomplete
            v-model="baseParams.omApplyNo"
            :fetch-suggestions="queryOmApplyNoSearch"
            placeholder="请输入或选择运管申请单号"
            clearable
            style="width: 100%"
            @select="handleOmApplyNoSelect"
          ></el-autocomplete>
        </template>

        <!-- 动态多方字段插槽 -->
        <template #contractParties>
          <div class="contract-parties">
            <div
              v-for="(party, index) in contractParties"
              :key="index"
              class="party-item"
            >
              <el-autocomplete
                v-model="party.value"
                :placeholder="`请输入${party.label}`"
                :fetch-suggestions="
                  (queryString, cb) => queryContractPartySearch(queryString, cb)
                "
                style="width: calc(100% - 40px); margin-right: 10px;"
                clearable
              >
                <template slot="prepend">{{ party.label }}</template>
              </el-autocomplete>
              <el-button
                v-if="index === 0 && contractParties.length < 10"
                @click="addContractParty"
                type="primary"
                icon="el-icon-plus"
                size="small"
                circle
              ></el-button>
              <el-button
                v-if="
                  contractParties.length > 2 &&
                    index === contractParties.length - 1
                "
                @click="removeContractParty(index)"
                type="danger"
                icon="el-icon-minus"
                size="small"
                circle
                style="margin-left: 5px;"
              ></el-button>
            </div>
          </div>
        </template>
      </DynamicForm>
    </el-card>

    <!-- 其他信息 -->
    <el-card id="otherInfo" class="mt20">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <ContractOtherInfo
        ref="otherInfo"
        :contractType="baseParams.contractType"
        v-model="otherParams"
      />
    </el-card>

    <!-- 操作按钮 -->
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading">
        取 消
      </el-button>
      <el-button @click="submit(0)" size="medium" :loading="btnLoading"
        >保存</el-button
      >
      <el-button
        @click="submit(1)"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import api from "@/api/infoArchive/contractAgreement/agreement.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import ContractOtherInfo from "./components/ContractOtherInfo.vue";

export default {
  name: "agreementAddPage",
  components: {
    ContractOtherInfo,
  },
  data() {
    return {
      contractId: "",
      btnLoading: false,
      pageLoading: false,
      type: "add",
      baseParams: {},
      otherParams: {},
      contractAgreementTypeOptions: [], // 合同协议类型字典
      businessTypeOptions: [], // 业务类型选项
      // 动态多方数据
      contractParties: [
        { label: "甲方", value: "", field: "partA" },
        { label: "乙方", value: "", field: "partB" },
      ],
      // 天干顺序用于命名
      tianGan: ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"],
    };
  },
  computed: {
    // 基本信息表单配置
    baseConfig() {
      return [
        {
          field: "omApplyNo",
          title: "运管申请单号",
          element: "slot",
          slotName: "omApplyNo",
        },
        {
          field: "contractCode",
          title: "合同编码",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.queryContractCodeSearch(queryString, cb);
            },
            clearable: true,
            placeholder: "请输入或选择合同编码",
          },
          attrs: {
            maxlength: 100,
          },
        },
        {
          field: "contractType",
          title: "合同协议类型",
          element: "el-select",
          props: {
            options: this.contractAgreementTypeOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            placeholder: "请选择合同协议类型",
            clearable: true,
          },
          rules: [
            {
              required: true,
              message: "请选择合同协议类型",
              trigger: "change",
            },
          ],
        },
        {
          field: "contractName",
          title: "合同协议名称",
          element: "el-input",
          attrs: {
            placeholder: "请输入合同协议名称，100个字符以内",
            maxlength: 100,
            showWordLimit: true,
          },
          rules: [
            {
              required: true,
              message: "请输入合同协议名称",
              trigger: "change",
            },
          ],
        },
        {
          field: "businessType",
          title: "业务类型",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.queryBusinessTypeSearch(queryString, cb);
            },
            clearable: true,
            placeholder: "请输入或选择业务类型",
          },
          attrs: {
            maxlength: 100,
          },
          rules: [
            {
              required: true,
              message: "请输入或选择业务类型",
              trigger: "change",
            },
          ],
        },
        {
          field: "file",
          element: "file-upload",
          title: "附件/图片",
          props: {
            limit: 20,
            accept: ".jpg, .jpeg, .png, .xls, .xlsx, .pdf, .doc, .docx",
            textTip:
              "支持批量上传，上传格式为jpg、jpeg、png、xls、xlsx、pdf、doc、docx文件，单个文件200M以内",
          },
          rules: [
            {
              required: true,
              message: "请上传",
              trigger: "change",
            },
          ],
        },
        {
          field: "effectiveTime",
          title: "合同&协议生效时间",
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: true,
              message: "请选择生效时间",
              trigger: "change",
            },
          ],
        },
        {
          field: "expireTime",
          title: "合同&协议失效时间",
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: true,
              message: "请选择失效时间",
              trigger: "change",
            },
          ],
        },
        {
          field: "contractCode",
          title: "合同编号",
          element: "el-input",
          rules: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "blur",
            },
          ],
        },
        {
          field: "contractParties",
          title: "合同协议方",
          element: "slot",
          slotName: "contractParties",
          rules: [
            {
              required: true,
              message: "请输入合同协议方",
              trigger: "change",
            },
          ],
        },
      ];
    },
    // 其他信息表单配置（暂时留空）
    otherConfig() {
      return [];
    },
  },
  created() {
    this.contractId = this.$route.query.contractId;
    this.type = this.$route.query.type || "add";

    // 初始化表单参数
    this.baseParams = {
      ...initParams(this.baseConfig),
    };
    this.otherParams = {
      ...initParams(this.otherConfig),
    };

    // 获取字典数据
    this.getDicts("contract_type").then((response) => {
      this.contractAgreementTypeOptions = response.data;
    });

    // 获取业务类型数据
    this.getBusinessTypeOptions();

    // 如果是编辑模式，获取详情数据
    if (this.type === "edit" && this.contractId) {
      this.getDetail();
    }
  },
  methods: {
    // 获取业务类型选项
    getBusinessTypeOptions() {
      queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
        this.businessTypeOptions = res.data;
      });
    },

    // 运管申请单号搜索
    queryOmApplyNoSearch(queryString, cb) {
      api
        .queryOmApplyNoOptions({
          name: queryString,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x.omApplyNo || x };
          });
          cb(result);
        });
    },

    // 合同编码搜索
    queryContractCodeSearch(queryString, cb) {
      api
        .queryContractCodeOptions({
          name: queryString,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x.contractCode || x };
          });
          cb(result);
        });
    },

    // 业务类型搜索
    queryBusinessTypeSearch(queryString, cb) {
      queryTreeList({
        pageSize: 99999,
        pageNum: 1,
      }).then((res) => {
        const options =
          res.data?.map((x) => {
            return { value: x.typeName };
          }) || [];
        const results = queryString
          ? options.filter((item) =>
              item.value.toLowerCase().includes(queryString.toLowerCase())
            )
          : options;

        cb(results);
      });
    },

    // 合同协议方搜索
    queryContractPartySearch(queryString, cb) {
      api
        .queryContractParty({
          name: queryString,
          pageNum: 1,
          pageSize: 10,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },

    // 处理运管申请单号选择后的自动填充
    handleOmApplyNoSelect(omApplyNo) {
      if (!omApplyNo) return;

      // 根据运管申请单号查询基本信息并自动填充
      api
        .getOmApplyNoInfo({ omApplyNo })
        .then((res) => {
          if (res?.code === "10000" && res.data) {
            const data = res.data;
            // 自动填充相关字段
            if (data.businessType) {
              this.baseParams.businessType = data.businessType;
            }
            if (data.contractName) {
              this.baseParams.contractName = data.contractName;
            }
            if (data.contractType) {
              this.baseParams.contractType = data.contractType;
            }
            if (data.effectiveTime) {
              this.baseParams.effectiveTime = data.effectiveTime;
            }
            if (data.expireTime) {
              this.baseParams.expireTime = data.expireTime;
            }
            // 自动填充合同协议方信息
            if (data.partA || data.partB) {
              this.contractParties = [
                { label: "甲方", value: data.partA || "", field: "partA" },
                { label: "乙方", value: data.partB || "", field: "partB" },
              ];

              // 如果有更多方，继续添加
              for (let i = 2; i < 10; i++) {
                const fieldName = "part" + this.tianGan[i];
                if (data[fieldName]) {
                  this.contractParties.push({
                    label: this.tianGan[i] + "方",
                    value: data[fieldName],
                    field: fieldName,
                  });
                }
              }
            }

            this.$message.success("已自动填充相关信息");
          }
        })
        .catch((error) => {
          console.error("获取运管申请单号信息失败:", error);
        });
    },

    // 添加合同协议方
    addContractParty() {
      if (this.contractParties.length < 10) {
        const index = this.contractParties.length;
        const label = this.tianGan[index] + "方";
        const field = "part" + this.tianGan[index];
        this.contractParties.push({
          label: label,
          value: "",
          field: field,
        });
      }
    },

    // 移除合同协议方
    removeContractParty(index) {
      if (this.contractParties.length > 2) {
        this.contractParties.splice(index, 1);
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取详情数据（编辑模式）
    async getDetail() {
      if (!this.contractId) return;
      this.pageLoading = true;
      try {
        const res = await api.getContractDetail({
          contractId: this.contractId,
        });
        this.pageLoading = false;
        if (res?.code === "10000") {
          const data = res.data;
          // 回显基本信息
          this.baseParams = { ...this.baseParams, ...data };

          // 回显合同协议方信息
          this.contractParties = [
            { label: "甲方", value: data.partA || "", field: "partA" },
            { label: "乙方", value: data.partB || "", field: "partB" },
          ];

          // 如果有更多方，继续添加
          for (let i = 2; i < 10; i++) {
            const fieldName = "part" + this.tianGan[i];
            if (data[fieldName]) {
              this.contractParties.push({
                label: this.tianGan[i] + "方",
                value: data[fieldName],
                field: fieldName,
              });
            }
          }
        }
      } catch (error) {
        this.pageLoading = false;
        console.error("获取详情失败:", error);
      }
    },

    // 提交保存
    async submit() {
      console.log("提交数据:", this.baseParams, this.otherParams);

      // 表单验证
      const baseValid = await this.$refs.baseForm.validate().catch(() => false);
      if (!baseValid) {
        return;
      }

      // 其他信息验证
      const otherValid = await this.$refs.otherInfo
        .validate()
        .catch(() => false);
      if (!otherValid) {
        this.$message.warning("其他信息验证失败");
        return;
      }

      this.$confirm(`是否确认保存？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        // 组装提交数据
        const params = {
          ...this.baseParams,
          ...this.otherParams,
        };

        // 添加合同协议方数据
        this.contractParties.forEach((party) => {
          if (party.value) {
            params[party.field] = party.value;
          }
        });

        // 如果是编辑模式，添加contractId
        if (this.type === "edit" && this.contractId) {
          params.contractId = this.contractId;
        }

        this.btnLoading = true;
        try {
          const res = await api.saveContract(params);
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.goBack();
          }
        } catch (error) {
          this.btnLoading = false;
          console.error("保存失败:", error);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contract-parties {
  .party-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
