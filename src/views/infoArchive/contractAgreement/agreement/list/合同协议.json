{"item": [{"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/queryList"}}, "response": [{"name": "分页查询合同-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "queryList"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/queryList"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"contractId\": 0, //主键\n            \"contractNo\": \"\", //合同编码\n            \"contractName\": \"\", //合同协议名称\n            \"attribute\": \"\", //属性\n            \"businessType\": \"\", //业务类型\n            \"contractTypeName\": \"\", //合同类型名称\n            \"stationCode\": \"\", //站点编码\n            \"stationName\": \"\", //站点名称\n            \"contractParty\": \"\", //合同协议方\n            \"effectiveTime\": \"\", //生效时间\n            \"expireTime\": \"\", //失效时间\n            \"omApplyNo\": \"\", //运管申请单号\n            \"changeApplyNo\": \"\", //变更申请单号\n            \"applyStatus\": \"\", //审批状态\n            \"status\": \"\", //合同协议状态\n            \"submitUser\": \"\", //提交人\n            \"submitTime\": \"\", //提交时间\n            \"createWay\": \"\", //创建方式，字典contract_create_way\n            \"warningFlag\": \"\", //预警标识，预警标识(0:未预警 1:已预警)\n            \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n            \"salesManager\": \"\", //销售经理\n            \"remark\": \"\", //备注\n            \"tenantId\": 0, //租户号\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\", //更新时间\n            \"wbtContractNo\": \"\", //维保通合同协议编号\n            \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n            \"changeFlag\": \"\", //是否变更过，Y：是，N：否\n            \"changeReason\": \"\", //变更原因\n            \"changeDate\": \"\", //变更日期\n            \"channelInfo\": \"\", //渠道信息\n            \"projectName\": \"\", //项目名称\n            \"constructionCode\": \"\", //在建工程编码，多个用逗号分隔\n            \"contractAmountOrRatio\": \"\", //合同金额/比例\n            \"agreementType\": \"\", //协议类型，字典agreement_type\n            \"businessPolicy\": \"\", //商务政策(金额/分润比例)\n            \"attributeName\": \"\", //属性名称\n            \"createWayName\": \"\",\n            \"partA\": \"\", //甲方\n            \"partB\": \"\", //乙方\n            \"partC\": \"\", //丙方\n            \"partD\": \"\", //丁方\n            \"partE\": \"\", //戊方\n            \"companyName\": \"\",\n            \"invoiceTypeName\": \"\",\n            \"protocolTypeName\": \"\",\n            \"signTime\": \"\",\n            \"startTime\": \"\",\n            \"endTime\": \"\",\n            \"isProjAssetName\": \"\", //项目下固定资产采购\n            \"factoryNo\": \"\", //供应商编码\n            \"factoryName\": \"\", //供应商名称\n            \"alarmCount\": 0, //预警数量\n            \"firstPartyRegistration\": \"\", //甲方注册地\n            \"contractDate\": \"\", //签约日期\n            \"contractPeriod\": \"\", //合同期限\n            \"isIcb\": \"\", //是否内部转包\n            \"businessLine\": \"\", //业务线\n            \"paymentTime\": \"\", //付款日期\n            \"payAmounts\": \"\", //本次申请金碟已付款金额\n            \"payAmountTotal\": \"\", //金碟累计已付款总金额\n            \"applyDept\": \"\", //申请部门\n            \"company\": \"\", //内部渠道公司\n            \"cooperationDept\": \"\", //合作部门\n            \"projName\": \"\", //项目名称\n            \"businessManager\": \"\", //商务经理\n            \"businessId\": \"\", //钉钉踏勘审批单号\n            \"agentName\": \"\", //渠道信息\n            \"cnConstructModelName\": \"\", //投建模型名称\n            \"agentCompanyName\": \"\", //产权方/产权代理方公司名称\n            \"belongCompany\": \"\", //归属公司\n            \"costCenterName\": \"\", //成本中心名称\n            \"mainContractNo\": \"\", //主申请编号\n            \"totalAmountBefore\": \"\", //原合同金额\n            \"totalAmount\": \"\", //剩余合同金额\n            \"projManagerName\": \"\", //项目经理\n            \"stopType\": \"\", //终止类型\n            \"stopApplyStatus\": \"\", //终止审批状态\n            \"signingPartyName\": \"\", //合同签署方\n            \"isArchived\": \"\", //归档状态\n            \"protocolOperatorName\": \"\", //操作人员名称\n            \"profitCenterName\": \"\", //利润中心名称\n            \"xyfCount\": \"\", //协议方数名称\n            \"customerName\": \"\", //客户名称\n            \"registrationAddress\": \"\", //注册地址\n            \"protocolOtherCode\": \"\", //子公司协议编码\n            \"signPurpose\": \"\", //协议签署目的\n            \"isAreaPt\": \"\", //是否有区域排他性\n            \"isIndustryPt\": \"\", //是否有行业排他性\n            \"isIntellectProperty\": \"\", //是否涉及知识产权\n            \"isDefyPunish\": \"\", //是否涉及违约处罚\n            \"isInvest\": \"\", //是否我方资金投入\n            \"rentalFee\": \"\", //场地租赁费/分成比例\n            \"toujianxieyiList\": [ //投建协议详情\n                {\n                    \"detailId\": 0, //主键ID\n                    \"contractId\": 0, //合同协议关联id\n                    \"stationName\": \"\", //场所名称\n                    \"stationCode\": \"\", //场所编码\n                    \"region\": \"\", //区域\n                    \"rentalFeeOrSplitRatio\": 0.0, //场地租赁费/分成比例，单位%\n                    \"meterOwner\": \"\", //电表户号归属方\n                    \"billingMethod\": \"\", //电费计费方式，字典billing_method\n                    \"billingStandard\": 0.0, //电费计费标准，单位元/kWh\n                    \"hasLateFee\": \"\", //是否有滞纳金，Y：是，N：否\n                    \"revenueModel\": \"\", //收益模式，字典revenue_model\n                    \"pileCount\": 0, //直流/交流桩数\n                    \"gunCount\": 0, //枪数\n                    \"tenantId\": 0, //租户号\n                    \"createTime\": \"\", //创建时间\n                    \"updateTime\": \"\" //更新时间\n                }\n            ],\n            \"docList\": [ //附件\n                {\n                    \"docId\": 0, //主键id\n                    \"docClassify\": \"\", //图档分类代码/crm_doc_classify\n                    \"docName\": \"\", //图档名称\n                    \"docType\": \"\", //图档分类代码/crm_doc_type  1: 业务照片  2.电表照片\n                    \"directory\": \"\", //文件目录/cm_doc_directory\n                    \"storeType\": \"\", //存储方式/crm_store_type, 0-oss,1-ftp,2-minio\n                    \"storePath\": \"\", //文件地址\n                    \"relaBizId\": 0, //业务主键id\n                    \"businessNo\": 0, //工单编号\n                    \"projectId\": 0, //项目id\n                    \"taskDefKey\": \"\", //任务key\n                    \"delFlag\": \"\", //删除标志（0正常 1删除）\n                    \"uploader\": 0, //上传人账号标识\n                    \"uploaderName\": \"\",\n                    \"uploadTime\": \"\", //上传时间\n                    \"remark\": \"\", //文档备注\n                    \"tenantId\": 0, //租户号\n                    \"createTime\": \"\", //创建时间\n                    \"updateTime\": \"\", //更新时间\n                    \"businessType\": \"\", //业务类型\n                    \"previewPath\": \"\", //文件预览地址\n                    \"completionAuditReason\": \"\" //文件预览地址\n                }\n            ],\n            \"changeDocList\": [ //变更附件\n                {\n                    \"docId\": 0, //主键id\n                    \"docClassify\": \"\", //图档分类代码/crm_doc_classify\n                    \"docName\": \"\", //图档名称\n                    \"docType\": \"\", //图档分类代码/crm_doc_type  1: 业务照片  2.电表照片\n                    \"directory\": \"\", //文件目录/cm_doc_directory\n                    \"storeType\": \"\", //存储方式/crm_store_type, 0-oss,1-ftp,2-minio\n                    \"storePath\": \"\", //文件地址\n                    \"relaBizId\": 0, //业务主键id\n                    \"businessNo\": 0, //工单编号\n                    \"projectId\": 0, //项目id\n                    \"taskDefKey\": \"\", //任务key\n                    \"delFlag\": \"\", //删除标志（0正常 1删除）\n                    \"uploader\": 0, //上传人账号标识\n                    \"uploaderName\": \"\",\n                    \"uploadTime\": \"\", //上传时间\n                    \"remark\": \"\", //文档备注\n                    \"tenantId\": 0, //租户号\n                    \"createTime\": \"\", //创建时间\n                    \"updateTime\": \"\", //更新时间\n                    \"businessType\": \"\", //业务类型\n                    \"previewPath\": \"\", //文件预览地址\n                    \"completionAuditReason\": \"\" //文件预览地址\n                }\n            ]\n        }\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "分页查询合同"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "remark"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/remark"}}, "response": [{"name": "添加备注-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "remark"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/remark"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "添加备注"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "log"], "query": [{"key": "contractId", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/log"}}, "response": [{"name": "查看日志-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "log"], "query": [{"key": "contractId", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/log"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"orderRecordId\": 0, //主键id\n            \"orderNo\": \"\", //工单编号\n            \"operatorType\": \"\", //操作类型\n            \"operatorTypeName\": \"\", //操作类型名称\n            \"operatorUser\": 0, //操作人\n            \"operatorUserName\": \"\", //操作人姓名\n            \"operatorTime\": \"\", //操作时间\n            \"remark\": \"\", //备注\n            \"createBy\": 0, //创建人\n            \"createTime\": \"\" //创建时间\n        }\n    ],\n    \"traceId\": \"\"\n}"}], "name": "查看日志"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "applyInfo"], "query": [{"key": "omApplyNo", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/applyInfo"}}, "response": [{"name": "运管审批信息-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "applyInfo"], "query": [{"key": "omApplyNo", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/applyInfo"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "运管审批信息"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "associatedProjects"], "query": [{"key": "omApplyNo", "value": "", "equals": true, "description": ""}, {"key": "contractTypeName", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/associatedProjects"}}, "response": [{"name": "关联项目信息-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "associatedProjects"], "query": [{"key": "omApplyNo", "value": "", "equals": true, "description": ""}, {"key": "contractTypeName", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/associatedProjects"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "关联项目信息"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "basicDetail"], "query": [{"key": "contractId", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/basicDetail"}}, "response": [{"name": "详情-基本信息-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "basicDetail"], "query": [{"key": "contractId", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/basicDetail"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": {\n        \"contractId\": 0, //主键\n        \"contractNo\": \"\", //合同编码\n        \"contractName\": \"\", //合同协议名称\n        \"attribute\": \"\", //属性\n        \"businessType\": \"\", //业务类型\n        \"contractTypeName\": \"\", //合同类型名称\n        \"stationCode\": \"\", //站点编码\n        \"stationName\": \"\", //站点名称\n        \"contractParty\": \"\", //合同协议方\n        \"effectiveTime\": \"\", //生效时间\n        \"expireTime\": \"\", //失效时间\n        \"omApplyNo\": \"\", //运管申请单号\n        \"changeApplyNo\": \"\", //变更申请单号\n        \"applyStatus\": \"\", //审批状态\n        \"status\": \"\", //合同协议状态\n        \"submitUser\": \"\", //提交人\n        \"submitTime\": \"\", //提交时间\n        \"createWay\": \"\", //创建方式，字典contract_create_way\n        \"warningFlag\": \"\", //预警标识，预警标识(0:未预警 1:已预警)\n        \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n        \"salesManager\": \"\", //销售经理\n        \"remark\": \"\", //备注\n        \"tenantId\": 0, //租户号\n        \"createTime\": \"\", //创建时间\n        \"updateTime\": \"\", //更新时间\n        \"wbtContractNo\": \"\", //维保通合同协议编号\n        \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n        \"changeFlag\": \"\", //是否变更过，Y：是，N：否\n        \"changeReason\": \"\", //变更原因\n        \"changeDate\": \"\", //变更日期\n        \"channelInfo\": \"\", //渠道信息\n        \"projectName\": \"\", //项目名称\n        \"constructionCode\": \"\", //在建工程编码，多个用逗号分隔\n        \"contractAmountOrRatio\": \"\", //合同金额/比例\n        \"agreementType\": \"\", //协议类型，字典agreement_type\n        \"businessPolicy\": \"\", //商务政策(金额/分润比例)\n        \"attributeName\": \"\", //属性名称\n        \"createWayName\": \"\",\n        \"partA\": \"\", //甲方\n        \"partB\": \"\", //乙方\n        \"partC\": \"\", //丙方\n        \"partD\": \"\", //丁方\n        \"partE\": \"\", //戊方\n        \"companyName\": \"\",\n        \"invoiceTypeName\": \"\",\n        \"protocolTypeName\": \"\",\n        \"signTime\": \"\",\n        \"startTime\": \"\",\n        \"endTime\": \"\",\n        \"isProjAssetName\": \"\", //项目下固定资产采购\n        \"factoryNo\": \"\", //供应商编码\n        \"factoryName\": \"\", //供应商名称\n        \"alarmCount\": 0, //预警数量\n        \"firstPartyRegistration\": \"\", //甲方注册地\n        \"contractDate\": \"\", //签约日期\n        \"contractPeriod\": \"\", //合同期限\n        \"isIcb\": \"\", //是否内部转包\n        \"businessLine\": \"\", //业务线\n        \"paymentTime\": \"\", //付款日期\n        \"payAmounts\": \"\", //本次申请金碟已付款金额\n        \"payAmountTotal\": \"\", //金碟累计已付款总金额\n        \"applyDept\": \"\", //申请部门\n        \"company\": \"\", //内部渠道公司\n        \"cooperationDept\": \"\", //合作部门\n        \"projName\": \"\", //项目名称\n        \"businessManager\": \"\", //商务经理\n        \"businessId\": \"\", //钉钉踏勘审批单号\n        \"agentName\": \"\", //渠道信息\n        \"cnConstructModelName\": \"\", //投建模型名称\n        \"agentCompanyName\": \"\", //产权方/产权代理方公司名称\n        \"belongCompany\": \"\", //归属公司\n        \"costCenterName\": \"\", //成本中心名称\n        \"mainContractNo\": \"\", //主申请编号\n        \"totalAmountBefore\": \"\", //原合同金额\n        \"totalAmount\": \"\", //剩余合同金额\n        \"projManagerName\": \"\", //项目经理\n        \"stopType\": \"\", //终止类型\n        \"stopApplyStatus\": \"\", //终止审批状态\n        \"signingPartyName\": \"\", //合同签署方\n        \"isArchived\": \"\", //归档状态\n        \"protocolOperatorName\": \"\", //操作人员名称\n        \"profitCenterName\": \"\", //利润中心名称\n        \"xyfCount\": \"\", //协议方数名称\n        \"customerName\": \"\", //客户名称\n        \"registrationAddress\": \"\", //注册地址\n        \"protocolOtherCode\": \"\", //子公司协议编码\n        \"signPurpose\": \"\", //协议签署目的\n        \"isAreaPt\": \"\", //是否有区域排他性\n        \"isIndustryPt\": \"\", //是否有行业排他性\n        \"isIntellectProperty\": \"\", //是否涉及知识产权\n        \"isDefyPunish\": \"\", //是否涉及违约处罚\n        \"isInvest\": \"\", //是否我方资金投入\n        \"rentalFee\": \"\", //场地租赁费/分成比例\n        \"toujianxieyiList\": [ //投建协议详情\n            {\n                \"detailId\": 0, //主键ID\n                \"contractId\": 0, //合同协议关联id\n                \"stationName\": \"\", //场所名称\n                \"stationCode\": \"\", //场所编码\n                \"region\": \"\", //区域\n                \"rentalFeeOrSplitRatio\": 0.0, //场地租赁费/分成比例，单位%\n                \"meterOwner\": \"\", //电表户号归属方\n                \"billingMethod\": \"\", //电费计费方式，字典billing_method\n                \"billingStandard\": 0.0, //电费计费标准，单位元/kWh\n                \"hasLateFee\": \"\", //是否有滞纳金，Y：是，N：否\n                \"revenueModel\": \"\", //收益模式，字典revenue_model\n                \"pileCount\": 0, //直流/交流桩数\n                \"gunCount\": 0, //枪数\n                \"tenantId\": 0, //租户号\n                \"createTime\": \"\", //创建时间\n                \"updateTime\": \"\" //更新时间\n            }\n        ],\n        \"docList\": [ //附件\n            {\n                \"docId\": 0, //主键id\n                \"docClassify\": \"\", //图档分类代码/crm_doc_classify\n                \"docName\": \"\", //图档名称\n                \"docType\": \"\", //图档分类代码/crm_doc_type  1: 业务照片  2.电表照片\n                \"directory\": \"\", //文件目录/cm_doc_directory\n                \"storeType\": \"\", //存储方式/crm_store_type, 0-oss,1-ftp,2-minio\n                \"storePath\": \"\", //文件地址\n                \"relaBizId\": 0, //业务主键id\n                \"businessNo\": 0, //工单编号\n                \"projectId\": 0, //项目id\n                \"taskDefKey\": \"\", //任务key\n                \"delFlag\": \"\", //删除标志（0正常 1删除）\n                \"uploader\": 0, //上传人账号标识\n                \"uploaderName\": \"\",\n                \"uploadTime\": \"\", //上传时间\n                \"remark\": \"\", //文档备注\n                \"tenantId\": 0, //租户号\n                \"createTime\": \"\", //创建时间\n                \"updateTime\": \"\", //更新时间\n                \"businessType\": \"\", //业务类型\n                \"previewPath\": \"\", //文件预览地址\n                \"completionAuditReason\": \"\" //文件预览地址\n            }\n        ],\n        \"changeDocList\": [ //变更附件\n            {\n                \"docId\": 0, //主键id\n                \"docClassify\": \"\", //图档分类代码/crm_doc_classify\n                \"docName\": \"\", //图档名称\n                \"docType\": \"\", //图档分类代码/crm_doc_type  1: 业务照片  2.电表照片\n                \"directory\": \"\", //文件目录/cm_doc_directory\n                \"storeType\": \"\", //存储方式/crm_store_type, 0-oss,1-ftp,2-minio\n                \"storePath\": \"\", //文件地址\n                \"relaBizId\": 0, //业务主键id\n                \"businessNo\": 0, //工单编号\n                \"projectId\": 0, //项目id\n                \"taskDefKey\": \"\", //任务key\n                \"delFlag\": \"\", //删除标志（0正常 1删除）\n                \"uploader\": 0, //上传人账号标识\n                \"uploaderName\": \"\",\n                \"uploadTime\": \"\", //上传时间\n                \"remark\": \"\", //文档备注\n                \"tenantId\": 0, //租户号\n                \"createTime\": \"\", //创建时间\n                \"updateTime\": \"\", //更新时间\n                \"businessType\": \"\", //业务类型\n                \"previewPath\": \"\", //文件预览地址\n                \"completionAuditReason\": \"\" //文件预览地址\n            }\n        ]\n    },\n    \"traceId\": \"\"\n}"}], "name": "详情-基本信息"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "alarmDetail"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/alarmDetail"}}, "response": [{"name": "详情-预警信息-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "alarmDetail"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/alarmDetail"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"id\": 0, //主键\n            \"contractId\": 0, //合同唯一标识\n            \"alarmContent\": \"\", //预警内容\n            \"alarmTime\": \"\", //预警时间\n            \"status\": \"\", //状态,0：未解除，1：已解除\n            \"statusName\": \"\",\n            \"tenantId\": 0, //租户号\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\" //更新时间\n        }\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "详情-预警信息"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "on<PERSON>r<PERSON>ff"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/onOrOff"}}, "response": [{"name": "启用禁用 合同监控预警状态-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "on<PERSON>r<PERSON>ff"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/onOrOff"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "启用禁用 合同监控预警状态"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "businessType"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/businessType"}}, "response": [{"name": "业务类型列表-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "businessType"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/businessType"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        \"\"\n    ],\n    \"traceId\": \"\"\n}"}], "name": "业务类型列表"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "stationName"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/stationName"}}, "response": [{"name": "场站名称列表-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "stationName"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/stationName"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        \"\"\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "场站名称列表"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "applyStatus"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/applyStatus"}}, "response": [{"name": "运管审批状态列表-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "applyStatus"], "query": [{"key": "name", "value": "", "equals": true, "description": ""}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/applyStatus"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        \"\"\n    ],\n    \"traceId\": \"\"\n}"}], "name": "运管审批状态列表"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "contractParty"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/contractParty"}}, "response": [{"name": "合同协议方列表-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "contractParty"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/contractParty"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        \"\"\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "合同协议方列表"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "relatedContract"], "query": [{"key": "pageNum", "value": "", "equals": true, "description": ""}, {"key": "pageSize", "value": "", "equals": true, "description": ""}, {"key": "tenantId", "value": "", "equals": true, "description": ""}, {"key": "orgNo", "value": "", "equals": true, "description": ""}, {"key": "orgNoList[0]", "value": "", "equals": true, "description": ""}, {"key": "operatorId", "value": "", "equals": true, "description": ""}, {"key": "operatorName", "value": "", "equals": true, "description": ""}, {"key": "contractId", "value": "", "equals": true, "description": "主键"}, {"key": "contractNo", "value": "", "equals": true, "description": "合同编码"}, {"key": "contractName", "value": "", "equals": true, "description": "合同协议名称"}, {"key": "attribute", "value": "", "equals": true, "description": "属性"}, {"key": "businessType", "value": "", "equals": true, "description": "业务类型"}, {"key": "stationCode", "value": "", "equals": true, "description": "站点编码"}, {"key": "stationName", "value": "", "equals": true, "description": "站点名称"}, {"key": "contractParty", "value": "", "equals": true, "description": "合同协议方"}, {"key": "effectiveTime", "value": "", "equals": true, "description": "生效时间"}, {"key": "effectiveStartTime", "value": "", "equals": true, "description": ""}, {"key": "effectiveEndTime", "value": "", "equals": true, "description": ""}, {"key": "expireTime", "value": "", "equals": true, "description": "失效时间"}, {"key": "expireStartTime", "value": "", "equals": true, "description": ""}, {"key": "expireEndTime", "value": "", "equals": true, "description": ""}, {"key": "omApplyNo", "value": "", "equals": true, "description": "运管申请单号"}, {"key": "changeApplyNo", "value": "", "equals": true, "description": "变更申请单号"}, {"key": "applyStatus", "value": "", "equals": true, "description": "审批状态"}, {"key": "status", "value": "", "equals": true, "description": "合同协议状态"}, {"key": "submitUser", "value": "", "equals": true, "description": "提交人"}, {"key": "submitTime", "value": "", "equals": true, "description": "提交时间"}, {"key": "submitStartTime", "value": "", "equals": true, "description": ""}, {"key": "submitEndTime", "value": "", "equals": true, "description": ""}, {"key": "createWay", "value": "", "equals": true, "description": "创建方式，字典contract_create_way"}, {"key": "remark", "value": "", "equals": true, "description": "备注"}, {"key": "createTime", "value": "", "equals": true, "description": "创建时间"}, {"key": "name", "value": "", "equals": true, "description": ""}, {"key": "monitorStatus", "value": "", "equals": true, "description": "该合同预警监控状态，0：启用，1：禁用"}, {"key": "wbtContractNo", "value": "", "equals": true, "description": "维保通合同协议编号"}, {"key": "wbtChangeContractNo", "value": "", "equals": true, "description": "维保通合同协议变更编号"}, {"key": "changeFlag", "value": "", "equals": true, "description": "是否变更过，Y：是，N：否"}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/relatedContract"}}, "response": [{"name": "关联合同列表-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "relatedContract"], "query": [{"key": "pageNum", "value": "", "equals": true, "description": ""}, {"key": "pageSize", "value": "", "equals": true, "description": ""}, {"key": "tenantId", "value": "", "equals": true, "description": ""}, {"key": "orgNo", "value": "", "equals": true, "description": ""}, {"key": "orgNoList[0]", "value": "", "equals": true, "description": ""}, {"key": "operatorId", "value": "", "equals": true, "description": ""}, {"key": "operatorName", "value": "", "equals": true, "description": ""}, {"key": "contractId", "value": "", "equals": true, "description": "主键"}, {"key": "contractNo", "value": "", "equals": true, "description": "合同编码"}, {"key": "contractName", "value": "", "equals": true, "description": "合同协议名称"}, {"key": "attribute", "value": "", "equals": true, "description": "属性"}, {"key": "businessType", "value": "", "equals": true, "description": "业务类型"}, {"key": "stationCode", "value": "", "equals": true, "description": "站点编码"}, {"key": "stationName", "value": "", "equals": true, "description": "站点名称"}, {"key": "contractParty", "value": "", "equals": true, "description": "合同协议方"}, {"key": "effectiveTime", "value": "", "equals": true, "description": "生效时间"}, {"key": "effectiveStartTime", "value": "", "equals": true, "description": ""}, {"key": "effectiveEndTime", "value": "", "equals": true, "description": ""}, {"key": "expireTime", "value": "", "equals": true, "description": "失效时间"}, {"key": "expireStartTime", "value": "", "equals": true, "description": ""}, {"key": "expireEndTime", "value": "", "equals": true, "description": ""}, {"key": "omApplyNo", "value": "", "equals": true, "description": "运管申请单号"}, {"key": "changeApplyNo", "value": "", "equals": true, "description": "变更申请单号"}, {"key": "applyStatus", "value": "", "equals": true, "description": "审批状态"}, {"key": "status", "value": "", "equals": true, "description": "合同协议状态"}, {"key": "submitUser", "value": "", "equals": true, "description": "提交人"}, {"key": "submitTime", "value": "", "equals": true, "description": "提交时间"}, {"key": "submitStartTime", "value": "", "equals": true, "description": ""}, {"key": "submitEndTime", "value": "", "equals": true, "description": ""}, {"key": "createWay", "value": "", "equals": true, "description": "创建方式，字典contract_create_way"}, {"key": "remark", "value": "", "equals": true, "description": "备注"}, {"key": "createTime", "value": "", "equals": true, "description": "创建时间"}, {"key": "name", "value": "", "equals": true, "description": ""}, {"key": "monitorStatus", "value": "", "equals": true, "description": "该合同预警监控状态，0：启用，1：禁用"}, {"key": "wbtContractNo", "value": "", "equals": true, "description": "维保通合同协议编号"}, {"key": "wbtChangeContractNo", "value": "", "equals": true, "description": "维保通合同协议变更编号"}, {"key": "changeFlag", "value": "", "equals": true, "description": "是否变更过，Y：是，N：否"}], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/relatedContract"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": [\n        {\n            \"contractName\": \"\", //合约名称\n            \"contractNo\": \"\", //合约编号\n            \"businessType\": \"\", //业务类型\n            \"effectiveTime\": \"\", //有效期\n            \"applyNo\": \"\", //运管申请编号\n            \"saleManager\": \"\", //销售经理\n            \"submitter\": \"\", //提交人\n            \"submitTime\": \"\", //提交时间\n            \"approvalStatus\": \"\", //审批状态\n            \"protocolStatus\": \"\"\n        }\n    ],\n    \"traceId\": \"\",\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"total\": 0\n}"}], "name": "关联合同列表"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "updateExpire"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/updateExpire"}}, "response": [{"name": "定时任务 更新合同到期状态-Example", "originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["contractInfo", "updateExpire"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/updateExpire"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "定时任务 更新合同到期状态"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"pageNum\": 0,\n  \"pageSize\": 0,\n  \"tenantId\": 0,\n  \"orgNo\": 0,\n  \"orgNoList\": [\n    0\n  ],\n  \"operatorId\": 0,\n  \"operatorName\": \"\",\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"effectiveStartTime\": \"\",\n  \"effectiveEndTime\": \"\",\n  \"expireTime\": \"\",\n  \"expireStartTime\": \"\",\n  \"expireEndTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"submitStartTime\": \"\",\n  \"submitEndTime\": \"\",\n  \"createWay\": \"\",\n  \"remark\": \"\",\n  \"createTime\": \"\",\n  \"name\": \"\",\n  \"monitorStatus\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\"\n}"}, "url": {"path": ["contractInfo", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/export"}}, "response": [{"name": "导出-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"pageNum\": 0,\n    \"pageSize\": 0,\n    \"tenantId\": 0,\n    \"orgNo\": 0,\n    \"orgNoList\": [\n        0\n    ],\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"effectiveStartTime\": \"\",\n    \"effectiveEndTime\": \"\",\n    \"expireTime\": \"\", //失效时间\n    \"expireStartTime\": \"\",\n    \"expireEndTime\": \"\",\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"submitStartTime\": \"\",\n    \"submitEndTime\": \"\",\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"remark\": \"\", //备注\n    \"createTime\": \"\", //创建时间\n    \"name\": \"\",\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\" //是否变更过，Y：是，N：否\n}"}, "url": {"path": ["contractInfo", "export"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/export"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": \"\",\n    \"traceId\": \"\"\n}"}], "name": "导出"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"contractId\": 0,\n  \"contractNo\": \"\",\n  \"contractName\": \"\",\n  \"attribute\": \"\",\n  \"businessType\": \"\",\n  \"contractTypeName\": \"\",\n  \"stationCode\": \"\",\n  \"stationName\": \"\",\n  \"contractParty\": \"\",\n  \"effectiveTime\": \"\",\n  \"expireTime\": \"\",\n  \"omApplyNo\": \"\",\n  \"changeApplyNo\": \"\",\n  \"applyStatus\": \"\",\n  \"status\": \"\",\n  \"submitUser\": \"\",\n  \"submitTime\": \"\",\n  \"createWay\": \"\",\n  \"warningFlag\": \"\",\n  \"monitorStatus\": \"\",\n  \"salesManager\": \"\",\n  \"remark\": \"\",\n  \"tenantId\": 0,\n  \"createTime\": \"\",\n  \"updateTime\": \"\",\n  \"wbtContractNo\": \"\",\n  \"wbtChangeContractNo\": \"\",\n  \"changeFlag\": \"\",\n  \"changeReason\": \"\",\n  \"changeDate\": \"\",\n  \"channelInfo\": \"\",\n  \"projectName\": \"\",\n  \"constructionCode\": \"\",\n  \"contractAmountOrRatio\": \"\",\n  \"agreementType\": \"\",\n  \"businessPolicy\": \"\",\n  \"submitFlag\": \"\",\n  \"toujianxieyiList\": [\n    {\n      \"detailId\": 0,\n      \"contractId\": 0,\n      \"stationName\": \"\",\n      \"stationCode\": \"\",\n      \"region\": \"\",\n      \"rentalFeeOrSplitRatio\": 0.0,\n      \"meterOwner\": \"\",\n      \"billingMethod\": \"\",\n      \"billingStandard\": 0.0,\n      \"hasLateFee\": \"\",\n      \"revenueModel\": \"\",\n      \"pileCount\": 0,\n      \"gunCount\": 0,\n      \"tenantId\": 0,\n      \"createTime\": \"\",\n      \"updateTime\": \"\"\n    }\n  ],\n  \"docList\": [\n    {\n      \"docId\": 0,\n      \"docClassify\": \"\",\n      \"docName\": \"\",\n      \"docType\": \"\",\n      \"directory\": \"\",\n      \"storeType\": \"\",\n      \"storePath\": \"\",\n      \"relaBizId\": 0,\n      \"businessNo\": 0,\n      \"projectId\": 0,\n      \"taskDefKey\": \"\",\n      \"delFlag\": \"\",\n      \"uploader\": 0,\n      \"uploaderName\": \"\",\n      \"uploadTime\": \"\",\n      \"remark\": \"\",\n      \"tenantId\": 0,\n      \"createTime\": \"\",\n      \"updateTime\": \"\",\n      \"businessType\": \"\",\n      \"previewPath\": \"\",\n      \"completionAuditReason\": \"\"\n    }\n  ]\n}"}, "url": {"path": ["contractInfo", "save"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/save"}}, "response": [{"name": "新增/修改-Example", "originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"contractId\": 0, //主键\n    \"contractNo\": \"\", //合同编码\n    \"contractName\": \"\", //合同协议名称\n    \"attribute\": \"\", //属性\n    \"businessType\": \"\", //业务类型\n    \"contractTypeName\": \"\", //合同类型名称\n    \"stationCode\": \"\", //站点编码\n    \"stationName\": \"\", //站点名称\n    \"contractParty\": \"\", //合同协议方\n    \"effectiveTime\": \"\", //生效时间\n    \"expireTime\": \"\", //失效时间\n    \"omApplyNo\": \"\", //运管申请单号\n    \"changeApplyNo\": \"\", //变更申请单号\n    \"applyStatus\": \"\", //审批状态\n    \"status\": \"\", //合同协议状态\n    \"submitUser\": \"\", //提交人\n    \"submitTime\": \"\", //提交时间\n    \"createWay\": \"\", //创建方式，字典contract_create_way\n    \"warningFlag\": \"\", //预警标识，预警标识(0:未预警 1:已预警)\n    \"monitorStatus\": \"\", //该合同预警监控状态，0：启用，1：禁用\n    \"salesManager\": \"\", //销售经理\n    \"remark\": \"\", //备注\n    \"tenantId\": 0, //租户号\n    \"createTime\": \"\", //创建时间\n    \"updateTime\": \"\", //更新时间\n    \"wbtContractNo\": \"\", //维保通合同协议编号\n    \"wbtChangeContractNo\": \"\", //维保通合同协议变更编号\n    \"changeFlag\": \"\", //是否变更过，Y：是，N：否\n    \"changeReason\": \"\", //变更原因\n    \"changeDate\": \"\", //变更日期\n    \"channelInfo\": \"\", //渠道信息\n    \"projectName\": \"\", //项目名称\n    \"constructionCode\": \"\", //在建工程编码，多个用逗号分隔\n    \"contractAmountOrRatio\": \"\", //合同金额/比例\n    \"agreementType\": \"\", //协议类型，字典agreement_type\n    \"businessPolicy\": \"\", //商务政策(金额/分润比例)\n    \"submitFlag\": \"\", //是否提交(0:保存  1:提交)\n    \"toujianxieyiList\": [ //投建协议详情\n        {\n            \"detailId\": 0, //主键ID\n            \"contractId\": 0, //合同协议关联id\n            \"stationName\": \"\", //场所名称\n            \"stationCode\": \"\", //场所编码\n            \"region\": \"\", //区域\n            \"rentalFeeOrSplitRatio\": 0.0, //场地租赁费/分成比例，单位%\n            \"meterOwner\": \"\", //电表户号归属方\n            \"billingMethod\": \"\", //电费计费方式，字典billing_method\n            \"billingStandard\": 0.0, //电费计费标准，单位元/kWh\n            \"hasLateFee\": \"\", //是否有滞纳金，Y：是，N：否\n            \"revenueModel\": \"\", //收益模式，字典revenue_model\n            \"pileCount\": 0, //直流/交流桩数\n            \"gunCount\": 0, //枪数\n            \"tenantId\": 0, //租户号\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\" //更新时间\n        }\n    ],\n    \"docList\": [ //附件\n        {\n            \"docId\": 0, //主键id\n            \"docClassify\": \"\", //图档分类代码/crm_doc_classify\n            \"docName\": \"\", //图档名称\n            \"docType\": \"\", //图档分类代码/crm_doc_type  1: 业务照片  2.电表照片\n            \"directory\": \"\", //文件目录/cm_doc_directory\n            \"storeType\": \"\", //存储方式/crm_store_type, 0-oss,1-ftp,2-minio\n            \"storePath\": \"\", //文件地址\n            \"relaBizId\": 0, //业务主键id\n            \"businessNo\": 0, //工单编号\n            \"projectId\": 0, //项目id\n            \"taskDefKey\": \"\", //任务key\n            \"delFlag\": \"\", //删除标志（0正常 1删除）\n            \"uploader\": 0, //上传人账号标识\n            \"uploaderName\": \"\",\n            \"uploadTime\": \"\", //上传时间\n            \"remark\": \"\", //文档备注\n            \"tenantId\": 0, //租户号\n            \"createTime\": \"\", //创建时间\n            \"updateTime\": \"\", //更新时间\n            \"businessType\": \"\", //业务类型\n            \"previewPath\": \"\", //文件预览地址\n            \"completionAuditReason\": \"\" //文件预览地址\n        }\n    ]\n}"}, "url": {"path": ["contractInfo", "save"], "query": [], "host": ["{{charging-maintenance-web}}"], "raw": "{{charging-maintenance-web}}/contractInfo/save"}}, "code": 200, "_postman_previewlanguage": "json", "header": [{"name": "date", "key": "date", "value": "周三, 18 6月 202516:46:32 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"success\": false,\n    \"code\": \"\",\n    \"message\": \"\",\n    \"data\": null,\n    \"traceId\": \"\"\n}"}], "name": "新增/修改"}], "info": {"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "name": "合同协议档案-20250618164632", "description": "合同协议档案"}}