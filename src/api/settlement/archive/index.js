import request from "@/utils/request";

/**
 * 结算材料归档管理API接口
 * 提供结算材料归档信息的增删改查功能
 */
export default {
  /**
   * 分页查询结算材料归档信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.companyCode] - 所属公司编码（字典：st_material_company）
   * @param {string} [data.bizType] - 所属业务编码（字典：st_material_biz）
   * @param {string} [data.customerCode] - 客户编码（字典：st_material_customer）
   * @param {string} [data.startDate] - 结算周期开始（格式：yyyy-MM-dd）
   * @param {string} [data.endDate] - 结算周期结束（格式：yyyy-MM-dd）
   * @param {string} [data.type] - 类型编码（字典：st_material_type）
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 结算材料归档信息列表
   * @returns {number} returns.data[].id - 记录ID
   * @returns {string} returns.data[].companyName - 所属公司名称
   * @returns {string} returns.data[].companyCode - 所属公司编码
   * @returns {string} returns.data[].bizTypeName - 所属业务名称
   * @returns {string} returns.data[].bizType - 所属业务编码
   * @returns {string} returns.data[].customerName - 客户名称
   * @returns {string} returns.data[].customerCode - 客户编码
   * @returns {string} returns.data[].startDate - 结算周期开始
   * @returns {string} returns.data[].endDate - 结算周期结束
   * @returns {string} returns.data[].type - 类型
   * @returns {string} returns.data[].docName - 文件名称
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {string} returns.data[].delFlag - 删除标志（0正常 1删除）
   * @returns {number} returns.data[].tenantId - 租户号
   * @returns {Array<Object>} returns.data[].materialDetailList - 材料明细列表
   * @returns {string} returns.data[].settlementCycle - 结算周期
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await archiveApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   companyCode: 'COMPANY001'
   * });
   */
  list(data) {
    return request({
      url: "/st/material/list",
      method: "post",
      data: data,
    });
  },

  /**
   * 新增结算材料归档信息
   * @param {Object} data - 结算材料归档信息数据
   * @param {string} data.companyCode - 所属公司编码（必填，字典：st_material_company）
   * @param {string} data.bizType - 所属业务编码（必填，字典：st_material_biz）
   * @param {string} data.customerCode - 客户编码（必填，字典：st_material_customer）
   * @param {string} data.startDate - 结算周期开始（必填，格式：yyyy-MM-dd）
   * @param {string} data.endDate - 结算周期结束（必填，格式：yyyy-MM-dd）
   * @param {string} data.type - 类型编码（必填，字典：st_material_type）
   * @param {string} data.docName - 文件名称（必填）
   * @param {Array<Object>} data.materialDetailList - 材料明细列表（必填）
   * @param {string} data.materialDetailList[].type - 材料类型（字典：st_material_type）
   * @param {string} data.materialDetailList[].fileName - 文件名称
   * @param {string} data.materialDetailList[].filePath - 文件地址
   * @returns {Promise<Object>} 返回新增结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string|number} returns.data - 新增记录的ID或操作结果
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增结算材料归档信息
   * const result = await archiveApi.add({
   *   companyCode: 'COMPANY001',
   *   bizType: 'BIZ001',
   *   customerCode: 'CUSTOMER001',
   *   startDate: '2024-01-01',
   *   endDate: '2024-01-31',
   *   type: 'TYPE001',
   *   docName: '结算材料文档',
   *   materialDetailList: [
   *     {
   *       type: 'TYPE001',
   *       fileName: '材料1.pdf',
   *       filePath: '/path/to/file1.pdf'
   *     }
   *   ]
   * });
   */
  add(data) {
    return request({
      url: "/st/material/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑结算材料归档信息
   * @param {Object} data - 结算材料归档信息数据
   * @param {number} data.id - 结算材料归档信息ID（必填）
   * @param {string} [data.companyCode] - 所属公司编码（字典：st_material_company）
   * @param {string} [data.bizType] - 所属业务编码（字典：st_material_biz）
   * @param {string} [data.customerCode] - 客户编码（字典：st_material_customer）
   * @param {string} [data.startDate] - 结算周期开始（格式：yyyy-MM-dd）
   * @param {string} [data.endDate] - 结算周期结束（格式：yyyy-MM-dd）
   * @param {string} [data.type] - 类型编码（字典：st_material_type）
   * @param {string} [data.docName] - 文件名称
   * @param {Array<Object>} [data.materialDetailList] - 材料明细列表
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑结算材料归档信息
   * const result = await archiveApi.edit({
   *   id: 1,
   *   docName: '更新后的文档名称'
   * });
   */
  edit(data) {
    return request({
      url: "/st/material/edit",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除结算材料归档信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的结算材料归档信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个结算材料归档信息
   * const result = await archiveApi.delete({ id: 1 });
   * 
   * // 批量删除结算材料归档信息
   * const result = await archiveApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/material/delete",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出结算材料归档信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.companyCode] - 所属公司编码筛选条件
   * @param {string} [data.bizType] - 所属业务编码筛选条件
   * @param {string} [data.customerCode] - 客户编码筛选条件
   * @param {string} [data.startDate] - 结算周期开始筛选条件
   * @param {string} [data.endDate] - 结算周期结束筛选条件
   * @param {string} [data.type] - 类型编码筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有结算材料归档信息
   * const result = await archiveApi.export({
   *   companyCode: 'COMPANY001'
   * });
   */
  export(data) {
    return request({
      url: "/st/material/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入结算材料归档信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await archiveApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/material/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 获取结算材料归档相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.companyNames] - 公司名称选项列表
   * @returns {Array<string>} [returns.data.bizTypes] - 业务类型选项列表
   * @returns {Array<string>} [returns.data.customerNames] - 客户名称选项列表
   * @returns {Array<string>} [returns.data.materialTypes] - 材料类型选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await archiveApi.getDropLists();
   * // result.data = {
   * //   companyNames: ['公司A', '公司B'],
   * //   bizTypes: ['业务类型1', '业务类型2'],
   * //   customerNames: ['客户A', '客户B'],
   * //   materialTypes: ['类型1', '类型2']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/material/getDropLists",
      method: "get",
    });
  },
};
