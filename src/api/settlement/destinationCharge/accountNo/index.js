import request from "@/utils/request";

/**
 * 目标收费户号管理API接口
 * 提供户号信息的增删改查功能
 */
export default {
  /**
   * 分页查询户号信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.stationCode] - 站点编号（模糊查询）
   * @param {string} [data.stationName] - 站点名称（模糊查询）
   * @param {string} [data.province] - 省份
   * @param {string} [data.city] - 市
   * @param {string} [data.startOnlineDate] - 上线开始时间（格式：YYYY-MM-DD）
   * @param {string} [data.endOnlineDate] - 上线结束时间（格式：YYYY-MM-DD）
   * @param {number} [data.creator] - 创建人ID
   * @param {string} [data.accountSubject] - 户号主体（模糊查询）
   * @param {string} [data.reportForm] - 报电形式（字典：report_form）
   * @param {string} [data.powerSupplyUnit] - 供电单位（模糊查询）
   * @param {string} [data.fieldName] - 查询字段名
   * @param {string} [data.fieldValue] - 查询字段值
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 户号信息列表
   * @returns {number} returns.data[].accountId - 主键ID
   * @returns {string} returns.data[].stationCode - 站点编号
   * @returns {string} returns.data[].stationName - 站点名称
   * @returns {string} returns.data[].province - 省份
   * @returns {string} returns.data[].provinceName - 省份名称
   * @returns {string} returns.data[].city - 市
   * @returns {string} returns.data[].cityName - 市名称
   * @returns {string} returns.data[].onlineDate - 上线时间
   * @returns {string} returns.data[].accountNo - 电费户号
   * @returns {string} returns.data[].accountSubject - 户号主体
   * @returns {string} returns.data[].powerSupplyUnit - 供电单位
   * @returns {string} returns.data[].electricAddress - 用电地址
   * @returns {string} returns.data[].reportForm - 报电形式（字典：report_form）
   * @returns {string} returns.data[].reportFormName - 报电形式名称
   * @returns {string} returns.data[].remark - 备注（500个字符以内）
   * @returns {number} returns.data[].tenantId - 租户号
   * @returns {number} returns.data[].orgNo - 组织编号
   * @returns {string} returns.data[].orgNoName - 组织名称
   * @returns {number} returns.data[].creator - 创建人ID
   * @returns {string} returns.data[].creatorName - 创建人名称
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.data[].operatorId - 操作员ID
   * @returns {string} returns.data[].belongPlace - 所属区域
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await accountNoApi.queryList({
   *   pageNum: 1,
   *   pageSize: 10,
   *   stationName: '测试站点'
   * });
   */
  queryList(data) {
    return request({
      url: "/account/queryList",
      method: "post",
      data: data,
    });
  },

  /**
   * 分页查询充电平台的站点信息
   * 用于站点选择器的数据源，支持按站点编码和站点名称模糊搜索
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 运营商ID
   * @param {string} [data.operatorName] - 运营商名称
   * @param {string} [data.stationNo] - 站点编码（模糊查询）
   * @param {string} [data.stationName] - 站点名称（模糊查询）
   * @returns {Promise<Object>} 返回站点信息查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 站点信息列表
   * @returns {string} returns.data[].stationId - 站点ID
   * @returns {string} returns.data[].stationNo - 站点编码（运营商自编的编号）
   * @returns {string} returns.data[].stationName - 站点名称
   * @returns {string} returns.data[].stationType - 站点类型
   * @returns {number} returns.data[].pileNum - 站点充电桩个数
   * @returns {string} returns.data[].province - 省
   * @returns {string} returns.data[].city - 城市
   * @returns {string} returns.data[].county - 区/县
   * @returns {string} returns.data[].stationAddress - 地址：区下面的详细地址
   * @returns {string} returns.data[].operationStatus - 运营状态
   *   - '01': 建设中
   *   - '02': 运营
   *   - '03': 停运
   *   - '04': 检修
   *   - '05': 退运
   *   - '06': 已删除
   * @returns {string} returns.data[].buildDate - 建设时间
   * @returns {string} returns.data[].openDate - 投运时间
   * @returns {string} returns.data[].onlineDate - 上线日期
   * @returns {number} returns.data[].openFlag - 是否对外开放（1是0否）
   * @returns {string} returns.data[].stationChargeType - 站点充电分类
   *   - '01': 交流充电站
   *   - '02': 直流充电桩
   *   - '03': 交直流混合充电站
   * @returns {string} returns.data[].businessTime - 运营时间
   * @returns {string} returns.data[].construction - 建设场所（字典：construction）
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询站点信息用于选择器
   * const result = await accountNoApi.queryStationList({
   *   pageNum: 1,
   *   pageSize: 20,
   *   stationName: '测试'
   * });
   */
  queryStationList(data) {
    return request({
      url: "/st/stationSettlementAnalysis/queryStationList",
      method: "post",
      data,
    });
  },

  /**
   * 分页查询字段去重值
   * 用于获取户号、户号主体、供电单位等字段的去重值列表
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.stationCode] - 站点编号
   * @param {string} [data.stationName] - 站点名称
   * @param {string} [data.province] - 省份
   * @param {string} [data.city] - 市
   * @param {string} [data.startOnlineDate] - 上线开始时间
   * @param {string} [data.endOnlineDate] - 上线结束时间
   * @param {number} [data.creator] - 创建人
   * @param {string} [data.accountSubject] - 户号主体
   * @param {string} [data.reportForm] - 报电形式
   * @param {string} [data.powerSupplyUnit] - 供电单位
   * @param {string} data.fieldName - 查询字段名（必填）
   *   - 'account_no': 电费户号
   *   - 'account_subject': 户号主体
   *   - 'power_supply_unit': 供电单位
   * @param {string} [data.fieldValue] - 查询字段值
   * @returns {Promise<Object>} 返回字段去重值查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<string>} returns.data - 字段去重值列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @example
   * // 查询户号主体的去重值
   * const result = await accountNoApi.queryDistinctValue({
   *   fieldName: 'account_subject'
   * });
   */
  queryDistinctValue(data) {
    return request({
      url: "/account/queryDistinctValue",
      method: "post",
      data: data,
    });
  },

  /**
   * 新增/编辑户号信息
   * @param {Object} data - 户号信息数据
   * @param {number} [data.accountId] - 主键ID（编辑时必填）
   * @param {string} data.stationCode - 站点编号（必填）
   * @param {string} data.stationName - 站点名称（必填）
   * @param {string} [data.province] - 省份
   * @param {string} [data.provinceName] - 省份名称
   * @param {string} [data.city] - 市
   * @param {string} [data.cityName] - 市名称
   * @param {string} [data.onlineDate] - 上线时间（格式：YYYY-MM-DD）
   * @param {string} data.accountNo - 电费户号（必填）
   * @param {string} [data.accountSubject] - 户号主体
   * @param {string} [data.powerSupplyUnit] - 供电单位
   * @param {string} [data.electricAddress] - 用电地址
   * @param {string} [data.reportForm] - 报电形式（字典：report_form）
   * @param {string} [data.reportFormName] - 报电形式名称
   * @param {string} [data.remark] - 备注（500个字符以内）
   * @param {number} [data.tenantId] - 租户号
   * @param {number} [data.orgNo] - 组织编号
   * @param {string} [data.orgNoName] - 组织名称
   * @param {number} [data.creator] - 创建人ID
   * @param {string} [data.creatorName] - 创建人名称
   * @param {string} [data.createTime] - 创建时间
   * @param {string} [data.updateTime] - 更新时间
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.belongPlace] - 所属区域
   * @returns {Promise<Object>} 返回保存结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {null} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增户号信息
   * const result = await accountNoApi.update({
   *   stationCode: 'ST001',
   *   stationName: '测试站点',
   *   accountNo: '***********',
   *   accountSubject: '测试主体',
   *   powerSupplyUnit: '国网供电公司'
   * });
   */
  update(data) {
    return request({
      url: "/account/saveInfo",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除户号信息
   * @param {Object} params - 删除参数
   * @param {number} params.accountId - 要删除的户号信息ID（必填）
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {null} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除户号信息
   * const result = await accountNoApi.remove({ accountId: 1 });
   */
  remove(params) {
    return request({
      url: "/account/remove",
      method: "get",
      params: params,
    });
  },

  /**
   * 导出户号信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {number} [data.pageNum] - 页码
   * @param {number} [data.pageSize] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.stationCode] - 站点编号
   * @param {string} [data.stationName] - 站点名称
   * @param {string} [data.province] - 省份
   * @param {string} [data.city] - 市
   * @param {string} [data.startOnlineDate] - 上线开始时间
   * @param {string} [data.endOnlineDate] - 上线结束时间
   * @param {number} [data.creator] - 创建人
   * @param {string} [data.accountSubject] - 户号主体
   * @param {string} [data.reportForm] - 报电形式
   * @param {string} [data.powerSupplyUnit] - 供电单位
   * @param {string} [data.fieldName] - 查询字段名
   * @param {string} [data.fieldValue] - 查询字段值
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有户号信息
   * const result = await accountNoApi.exportData({
   *   stationName: '测试站点'
   * });
   */
  exportData(data) {
    return request({
      url: "/account/export",
      method: "post",
      data: data,
    });
  },
};
