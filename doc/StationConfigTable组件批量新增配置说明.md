# StationConfigTable 组件批量新增配置说明

## 概述

StationConfigTable 是一个基于 VXE-Table 的可编辑表格组件，专门用于批量新增功能。该组件支持动态列配置、表单验证、行操作等功能，广泛应用于结算页面的批量数据录入场景。

## 组件结构

### 核心组件
- **StationConfigTable**: 主表格组件 (`src/components/StationConfigTable/index.vue`)
- **BatchAddDrawer**: 批量新增抽屉组件 (`src/views/settlement/xdt/storageEnergy/components/BatchAddDrawer.vue`)

### 技术栈
- VXE-Table: 表格渲染和编辑
- Element UI: 表单控件和验证
- Vue.js: 组件框架

## batchAddColumns 配置结构

### 基础配置项

```javascript
{
  title: "列标题",           // 必填：列显示标题
  field: "fieldName",       // 必填：字段名称
  width: 150,              // 必填：列宽度（像素）
  isEdit: true,            // 必填：是否可编辑
  element: "el-input",     // 必填：编辑控件类型
  type: "checkbox",        // 可选：特殊列类型（如复选框）
  fixed: "left",           // 可选：列固定位置
  rules: [],               // 可选：表单验证规则
  props: {},               // 可选：控件属性配置
}
```

### 支持的 element 类型及标准 width 值

#### 1. 输入类控件
- **el-input**: 120px (单行文本)
- **el-input (textarea)**: 200px (多行文本)
- **el-input-number**: 150px (数字输入)

#### 2. 选择类控件
- **el-select**: 120px (下拉选择)
- **el-autocomplete**: 150px (自动完成输入)

#### 3. 日期时间控件
- **el-date-picker (date)**: 250px (日期选择)
- **el-date-picker (month)**: 250px (月份选择)
- **el-date-picker (datetime)**: 300px (日期时间选择)

#### 4. 特殊列类型
- **checkbox**: 60px (复选框列)

### 配置示例

#### 基础输入字段
```javascript
{
  title: "运营商",
  field: "operator",
  width: 120,
  isEdit: true,
  element: "el-input",
  rules: [{ required: true, message: "请输入运营商" }],
}
```

#### 数字输入字段
```javascript
{
  title: "充电量（kWh）",
  field: "chargingAmount",
  width: 150,
  isEdit: true,
  element: "el-input-number",
  props: {
    precision: 4,
    min: 0,
  },
}
```

#### 下拉选择字段
```javascript
{
  title: "结算方式",
  field: "settlementMethod",
  width: 120,
  isEdit: true,
  element: "el-select",
  props: {
    options: this.settlementMethodOptions,
    optionLabel: "dictLabel",
    optionValue: "dictValue",
    filterable: true,
  },
}
```

#### 日期选择字段
```javascript
{
  title: "账单月份",
  field: "billYearMonth",
  width: 250,
  isEdit: true,
  element: "el-date-picker",
  rules: [{ required: true, message: "请选择账单年月" }],
  props: {
    type: "month",
    valueFormat: "yyyy-MM",
  },
}
```

#### 自动完成输入字段
```javascript
{
  title: "站点名称",
  field: "siteName",
  width: 150,
  isEdit: true,
  element: "el-autocomplete",
  props: {
    fetchSuggestions: this.querySiteName,
  },
  rules: [{ required: true, message: "请输入场站名称" }],
}
```

#### 多行文本字段
```javascript
{
  title: "备注",
  field: "remarks",
  width: 200,
  isEdit: true,
  element: "el-input",
  props: {
    type: "textarea",
    rows: 2,
    maxlength: 500,
    showWordLimit: true,
    placeholder: "500个字符以内",
  },
}
```

## 组件使用方法

### 1. 在页面中引入组件
```javascript
import BatchAddDrawer from "./components/BatchAddDrawer.vue";

export default {
  components: {
    BatchAddDrawer,
  },
}
```

### 2. 在模板中使用
```html
<BatchAddDrawer
  ref="batchAddDrawer"
  title="批量新增标题"
  :columns="batchAddColumns"
  @submit="handleBatchAddSubmit"
/>
```

### 3. 实现批量新增方法
```javascript
methods: {
  // 打开批量新增抽屉
  handleBatchAdd() {
    this.$refs.batchAddDrawer.open();
  },

  // 处理批量新增提交
  async handleBatchAddSubmit(formData) {
    try {
      await api.batchAdd(formData);
      this.$message.success("批量新增成功");
      this.$refs.batchAddDrawer.handleClose();
      this.loadData();
    } catch (error) {
      console.error("批量新增失败:", error);
      this.$message.error("批量新增失败，请重试");
    }
  },
}
```

## 注意事项

### 1. 列宽度规范
- 严格按照上述标准 width 值配置，确保界面一致性
- 特殊情况下可适当调整，但需保持同类型控件宽度一致

### 2. 表单验证
- 必填字段需配置 `rules` 验证规则
- 验证规则支持 Element UI 的所有验证类型

### 3. 数据处理
- 组件会自动处理表单数据的双向绑定
- 提交时会进行表单验证，验证失败会阻止提交

### 4. 性能优化
- 大量数据时建议设置表格最大高度 (`max-height="500px"`)
- 复杂下拉选项建议使用 `filterable` 属性

### 5. 权限控制
- 批量新增按钮需配置相应的权限标识
- 格式：`v-has-permi="['模块:页面:batchAdd']"`

## 最佳实践

1. **配置复用**: 将 batchAddColumns 配置提取为计算属性，便于维护
2. **数据验证**: 合理配置验证规则，提升数据质量
3. **用户体验**: 提供清晰的错误提示和操作反馈
4. **代码规范**: 保持配置结构一致，便于团队协作
