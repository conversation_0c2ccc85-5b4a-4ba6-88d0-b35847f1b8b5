# 合同协议管理模块【新增/编辑】页面技术文档

## 概述

本文档描述了合同协议管理模块中新增/编辑页面的技术实现，包括页面结构、表单配置、API接口、路由配置和交互逻辑。

## 技术栈

- **前端框架**: Vue 2.x
- **UI组件库**: Element UI
- **表单组件**: DynamicForm（项目自定义组件）
- **文件上传**: file-upload（项目自定义组件）
- **路由管理**: Vue Router

## 文件结构

```
src/
├── views/infoArchive/contractAgreement/agreement/list/
│   ├── index.vue                 # 列表页面（已修改跳转逻辑）
│   └── add.vue                   # 新增/编辑页面（新建）
├── api/infoArchive/contractAgreement/
│   └── agreement.js              # API接口文件（已扩展）
├── router/
│   └── index.js                  # 路由配置（已添加新路由）
└── doc/
    └── 合同协议新增编辑页面技术文档.md  # 本文档
```

## 页面结构

### 1. 基本信息卡片

使用 `el-card` 组件包装，包含以下字段：

- **运管申请单号**: `el-autocomplete`，支持搜索和自动填充
- **运管变更申请单号**: `el-autocomplete`，支持搜索和自动填充
- **合同编码**: `el-autocomplete`，支持搜索
- **业务类型**: `el-autocomplete`，支持搜索
- **合同协议类型**: `el-select`，字典维护，必填
- **合同协议名称**: `el-input`，文本输入，必填，限制100字符
- **生效时间**: `el-date-picker`，日期选择
- **失效时间**: `el-date-picker`，日期选择
- **附件**: `file-upload`，支持多文件上传
- **合同协议方**: 动态多方字段，支持最多10方

### 2. 其他信息卡片

使用 `el-card` 组件包装，配置项暂时留空，后续补充。

### 3. 操作按钮

- **取消按钮**: 返回上一页
- **保存按钮**: 提交表单数据

## 表单配置

### DynamicForm 配置

```javascript
baseConfig() {
  return [
    {
      field: "omApplyNo",
      title: "运管申请单号",
      element: "slot",
      slotName: "omApplyNo"
    },
    {
      field: "changeApplyNo", 
      title: "运管变更申请单号",
      element: "slot",
      slotName: "changeApplyNo"
    },
    // ... 其他字段配置
  ];
}
```

### 动态多方字段实现

```javascript
// 天干顺序命名
tianGan: ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]

// 动态添加协议方
addContractParty() {
  if (this.contractParties.length < 10) {
    const index = this.contractParties.length;
    const label = this.tianGan[index] + "方";
    const field = "part" + this.tianGan[index];
    this.contractParties.push({
      label: label,
      value: "",
      field: field
    });
  }
}
```

## API接口

### 新增接口

1. **保存合同协议**
   ```javascript
   saveContract(data) {
     return request({
       url: "/contractInfo/save",
       method: "post",
       data
     });
   }
   ```

2. **查询合同协议详情**
   ```javascript
   getContractDetail(data) {
     return request({
       url: "/contractInfo/detail", 
       method: "get",
       params: data
     });
   }
   ```

3. **查询运管申请单号选项**
   ```javascript
   queryOmApplyNoOptions(data) {
     return request({
       url: "/contractInfo/omApplyNoOptions",
       method: "get", 
       params: data
     });
   }
   ```

4. **根据运管申请单号获取基本信息**
   ```javascript
   getOmApplyNoInfo(data) {
     return request({
       url: "/contractInfo/omApplyNoInfo",
       method: "get",
       params: data
     });
   }
   ```

5. **查询合同编码选项**
   ```javascript
   queryContractCodeOptions(data) {
     return request({
       url: "/contractInfo/contractCodeOptions",
       method: "get",
       params: data
     });
   }
   ```

### 现有接口复用

- `queryBusinessType`: 业务类型查询
- `queryContractParty`: 合同协议方查询

## 路由配置

```javascript
{
  path: "/infoArchive/contractAgreement/agreement/list/add",
  component: () => import("@/views/infoArchive/contractAgreement/agreement/list/add.vue"),
  name: "agreementAdd",
  meta: {
    title: "新增合同协议",
    icon: "dashboard", 
    noCache: false,
    affix: false
  }
},
{
  path: "/infoArchive/contractAgreement/agreement/list/edit",
  component: () => import("@/views/infoArchive/contractAgreement/agreement/list/add.vue"),
  name: "agreementEdit", 
  meta: {
    title: "编辑合同协议",
    icon: "dashboard",
    noCache: false,
    affix: false
  }
}
```

## 交互逻辑

### 1. 自动填充功能

当用户选择运管申请单号或运管变更申请单号时，系统会自动调用 `getOmApplyNoInfo` 接口获取相关信息并填充到表单中：

```javascript
handleOmApplyNoSelect(omApplyNo) {
  if (!omApplyNo) return;
  
  api.getOmApplyNoInfo({ omApplyNo }).then((res) => {
    if (res?.code === "10000" && res.data) {
      const data = res.data;
      // 自动填充相关字段
      if (data.businessType) {
        this.baseParams.businessType = data.businessType;
      }
      // ... 其他字段填充
      this.$message.success("已自动填充相关信息");
    }
  });
}
```

### 2. 编辑模式数据回显

当页面以编辑模式打开时，会根据 `contractId` 查询详情数据并回显：

```javascript
async getDetail() {
  if (!this.contractId) return;
  this.pageLoading = true;
  
  const res = await api.getContractDetail({
    contractId: this.contractId
  });
  
  if (res?.code === "10000") {
    const data = res.data;
    // 回显基本信息
    this.baseParams = { ...this.baseParams, ...data };
    // 回显合同协议方信息
    // ...
  }
}
```

### 3. 表单验证

使用 Element UI 的表单验证功能：

```javascript
async submit() {
  // 表单验证
  const baseValid = await this.$refs.baseForm.validate().catch(() => false);
  if (!baseValid) {
    return;
  }
  // 提交数据
}
```

## 字典数据

- **合同协议类型**: `contract_agreement_type`

## 文件上传配置

```javascript
{
  field: "file",
  element: "file-upload", 
  title: "附件",
  props: {
    limit: 20,
    accept: ".jpg, .jpeg, .png, .xls, .xlsx, .pdf, .doc, .docx",
    textTip: "支持批量上传，上传格式为jpg、jpeg、png、xls、xlsx、pdf、doc、docx文件"
  }
}
```

## 样式说明

- 使用 Less 预处理器
- 卡片标题使用蓝色装饰线
- 动态多方字段使用 flex 布局
- 操作按钮居中显示

## 注意事项

1. **字段名匹配**: 确保前端字段名与后端API字段名完全匹配
2. **表单验证**: 必填字段需要添加相应的验证规则
3. **错误处理**: 所有API调用都需要添加错误处理逻辑
4. **缓存配置**: 页面启用缓存功能（noCache: false）
5. **权限控制**: 需要配置相应的权限标识

## 后续扩展

1. **其他信息卡片**: 根据业务需求补充其他信息字段
2. **表单验证增强**: 添加更多业务逻辑验证
3. **文件预览**: 增加附件预览功能
4. **数据联动**: 增加更多字段间的联动逻辑
