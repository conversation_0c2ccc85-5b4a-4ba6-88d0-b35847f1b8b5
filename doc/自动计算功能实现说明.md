# 自动计算功能实现说明

## 概述

本文档说明了在两个结算页面中实现的自动计算功能，包括JavaScript精度问题的解决方案和表单字段的实时计算。

## 实现页面

### 1. 合伙人账单页面 (partnerBill/index.vue)

**路径**: `src/views/settlement/destinationCharge/partnerBill/index.vue`

**计算功能**:
- **计算公式**: 金额 = 枪数 × 单价
- **触发条件**: 当枪数或单价字段值发生变化时
- **精度处理**: 使用NP库解决JavaScript浮点数精度问题
- **数值格式**: 金额保留2位小数，使用四舍五入

**相关字段**:
- `gunCount` (枪数) - 输入字段，精度0位
- `unitPrice` (单价) - 输入字段，精度2位  
- `amount` (金额) - 计算结果字段，禁用状态，精度2位

### 2. 场站结算情况分析页面 (settleAnalysis/index.vue)

**路径**: `src/views/settlement/destinationCharge/settleAnalysis/index.vue`

**计算功能**:

#### 电损计算
- **计算公式**: 电损 = (场地方电量 - 平台充电电量) / 场地方电量 × 100%
- **触发条件**: 当场地方电量或平台充电电量发生变化时
- **精度处理**: 使用NP库解决JavaScript浮点数精度问题
- **数值格式**: 百分比形式，保留3位小数
- **特殊处理**: 当场地方电量为0时，电损设为0

#### 电费偏差计算
- **计算公式**: 电费偏差 = (电费结算金额 - 平台充电电费) / 电费结算金额 × 100%
- **触发条件**: 当电费结算金额或平台充电电费发生变化时
- **精度处理**: 使用NP库解决JavaScript浮点数精度问题
- **数值格式**: 百分比形式，保留3位小数
- **特殊处理**: 当电费结算金额为0时，电费偏差设为0

**相关字段**:
- `siteElectricity` (场地方电量) - 输入字段，精度3位
- `platformCharge` (平台充电电量) - 输入字段，精度3位
- `electricalLoss` (电损) - 计算结果字段，禁用状态，精度3位
- `feeSettle` (电费结算金额) - 输入字段，精度2位
- `platformFee` (平台充电电费) - 输入字段，精度2位
- `feeDeviation` (电费偏差) - 计算结果字段，禁用状态，精度3位

## 技术实现

### 1. 精度问题解决方案

使用项目中已集成的`number-precision`库（NP）来解决JavaScript浮点数计算精度问题：

```javascript
// 乘法运算
const amount = this.NP.times(gunCount, unitPrice);

// 减法运算
const difference = this.NP.minus(siteElectricity, platformCharge);

// 除法运算
const ratio = this.NP.divide(difference, siteElectricity);

// 四舍五入
const roundedAmount = this.NP.round(amount, 2);
```

### 2. 字段变化监听

在BuseCrud组件的formConfig中使用`on.change`事件监听字段变化：

```javascript
{
  field: "gunCount",
  title: "枪数",
  element: "el-input-number",
  props: {
    precision: 0,
  },
  on: {
    change: () => {
      this.calculateAmount();
    },
  },
}
```

### 3. 表单值设置

使用BuseCrud组件提供的`setFormFields`方法设置计算结果：

```javascript
this.$refs.crud.setFormFields({
  amount: roundedAmount
});
```

### 4. 计算方法实现

#### 合伙人账单金额计算

```javascript
calculateAmount() {
  const formFields = this.$refs.crud.getFormFields();
  const gunCount = formFields.gunCount || 0;
  const unitPrice = formFields.unitPrice || 0;
  
  // 使用NP库解决JavaScript浮点数精度问题
  const amount = this.NP.times(gunCount, unitPrice);
  
  // 四舍五入保留2位小数
  const roundedAmount = this.NP.round(amount, 2);
  
  // 设置计算结果到表单
  this.$refs.crud.setFormFields({
    amount: roundedAmount
  });
}
```

#### 电损计算

```javascript
calculateElectricalLoss() {
  const formFields = this.$refs.crud.getFormFields();
  const siteElectricity = formFields.siteElectricity || 0;
  const platformCharge = formFields.platformCharge || 0;
  
  if (siteElectricity === 0) {
    // 场地方电量为0时，电损设为0
    this.$refs.crud.setFormFields({
      electricalLoss: 0
    });
    return;
  }
  
  // 使用NP库解决JavaScript浮点数精度问题
  // 计算：(场地方电量 - 平台充电电量) / 场地方电量
  const difference = this.NP.minus(siteElectricity, platformCharge);
  const ratio = this.NP.divide(difference, siteElectricity);
  // 转换为百分比并保留3位小数
  const electricalLoss = this.NP.round(this.NP.times(ratio, 100), 3);
  
  // 设置计算结果到表单
  this.$refs.crud.setFormFields({
    electricalLoss: electricalLoss
  });
}
```

## 使用说明

### 1. 合伙人账单页面

1. 打开合伙人账单页面
2. 点击"新增"或"编辑"按钮打开表单
3. 输入枪数和单价
4. 金额字段会自动计算并显示结果
5. 金额字段为禁用状态，不可手动编辑

### 2. 场站结算情况分析页面

1. 打开场站结算情况分析页面
2. 点击"新增"或"编辑"按钮打开表单
3. 输入场地方电量和平台充电电量，电损会自动计算
4. 输入电费结算金额和平台充电电费，电费偏差会自动计算
5. 计算结果字段为禁用状态，不可手动编辑

## 注意事项

1. **精度处理**: 所有计算都使用NP库来避免JavaScript浮点数精度问题
2. **边界条件**: 对除数为0的情况进行了特殊处理，避免除零错误
3. **实时计算**: 字段值变化时立即触发计算，提供实时反馈
4. **字段状态**: 计算结果字段设置为禁用状态，防止用户手动修改
5. **数值格式**: 严格按照业务需求设置小数位数（金额2位，百分比3位）

## 扩展性

该实现方案具有良好的扩展性：

1. **新增计算字段**: 只需在formConfig中添加相应的on.change事件监听
2. **修改计算公式**: 在对应的计算方法中修改计算逻辑
3. **调整精度**: 修改NP.round方法的第二个参数
4. **添加验证**: 可在计算方法中添加数据验证逻辑

## 依赖项

- `number-precision`: JavaScript精度计算库
- `@bangdao/buse-components-element`: BuseCrud组件库
- `Element UI`: UI组件库

## 测试建议

1. 测试正常数值计算的准确性
2. 测试边界条件（如除数为0）
3. 测试精度问题（如0.1 + 0.2）
4. 测试大数值计算
5. 测试负数计算
6. 测试字段联动的实时性
