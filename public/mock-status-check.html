<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock系统状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .status-ok {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .status-warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .status-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .cleanup-btn {
            background-color: #dc3545;
        }
        .cleanup-btn:hover {
            background-color: #c82333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mock系统状态检查工具</h1>
        <p>此工具用于检查当前环境的Mock系统状态，帮助诊断BuseCrud组件页面卡死问题。</p>
        
        <div id="status-container">
            <div class="status-item">
                <strong>正在检查系统状态...</strong>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="checkStatus()">重新检查</button>
            <button onclick="cleanupServiceWorkers()" class="cleanup-btn">清理ServiceWorker</button>
            <button onclick="showDetails()">显示详细信息</button>
        </div>

        <div id="details" style="display: none;">
            <h3>详细信息</h3>
            <pre id="details-content"></pre>
        </div>

        <div id="logs">
            <h3>操作日志</h3>
            <pre id="log-content"></pre>
        </div>
    </div>

    <script>
        let logs = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            document.getElementById('log-content').textContent = logs.join('\n');
        }

        function addStatusItem(message, type = 'ok') {
            const container = document.getElementById('status-container');
            const item = document.createElement('div');
            item.className = `status-item status-${type}`;
            item.innerHTML = message;
            container.appendChild(item);
        }

        function clearStatus() {
            document.getElementById('status-container').innerHTML = '';
        }

        async function checkStatus() {
            clearStatus();
            log('开始检查Mock系统状态...');

            // 检查环境变量（模拟）
            const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const currentEnv = isDevelopment ? 'development' : 'production';
            
            addStatusItem(`<strong>当前环境:</strong> ${currentEnv}`, isDevelopment ? 'warning' : 'ok');

            // 检查ServiceWorker支持
            if ('serviceWorker' in navigator) {
                addStatusItem('✓ 浏览器支持ServiceWorker', 'ok');
                
                try {
                    // 检查已注册的ServiceWorker
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    
                    if (registrations.length === 0) {
                        addStatusItem('✓ 没有发现ServiceWorker注册', 'ok');
                        log('没有发现ServiceWorker注册');
                    } else {
                        addStatusItem(`⚠ 发现 ${registrations.length} 个ServiceWorker注册`, 'warning');
                        log(`发现 ${registrations.length} 个ServiceWorker注册`);
                        
                        registrations.forEach((reg, index) => {
                            const isMockSW = reg.scope.includes('sw-mock-manager') || 
                                           (reg.active && reg.active.scriptURL.includes('sw-mock-manager'));
                            
                            if (isMockSW) {
                                addStatusItem(`❌ 发现Mock系统ServiceWorker: ${reg.scope}`, 'error');
                                log(`发现Mock系统ServiceWorker: ${reg.scope}`);
                            } else {
                                addStatusItem(`ℹ ServiceWorker ${index + 1}: ${reg.scope}`, 'warning');
                                log(`ServiceWorker ${index + 1}: ${reg.scope}`);
                            }
                        });
                    }
                } catch (error) {
                    addStatusItem(`❌ 检查ServiceWorker时出错: ${error.message}`, 'error');
                    log(`检查ServiceWorker时出错: ${error.message}`);
                }
            } else {
                addStatusItem('ℹ 浏览器不支持ServiceWorker', 'warning');
                log('浏览器不支持ServiceWorker');
            }

            // 检查Mock系统全局对象
            if (window.MockSystem) {
                addStatusItem('⚠ 发现Mock系统全局对象', 'warning');
                log('发现Mock系统全局对象');
                
                try {
                    const status = window.MockSystem.status();
                    if (status.enabled) {
                        addStatusItem('❌ Mock系统处于启用状态', 'error');
                        log('Mock系统处于启用状态');
                    } else {
                        addStatusItem('✓ Mock系统处于禁用状态', 'ok');
                        log('Mock系统处于禁用状态');
                    }
                } catch (error) {
                    addStatusItem(`⚠ 无法获取Mock系统状态: ${error.message}`, 'warning');
                    log(`无法获取Mock系统状态: ${error.message}`);
                }
            } else {
                addStatusItem('✓ 没有发现Mock系统全局对象', 'ok');
                log('没有发现Mock系统全局对象');
            }

            // 建议
            const hasIssues = document.querySelectorAll('.status-error').length > 0;
            if (hasIssues) {
                addStatusItem('🔧 <strong>建议:</strong> 发现问题，建议清理ServiceWorker并刷新页面', 'error');
                log('发现问题，建议清理ServiceWorker');
            } else {
                addStatusItem('✅ <strong>状态良好:</strong> 没有发现Mock系统相关问题', 'ok');
                log('系统状态良好');
            }

            log('状态检查完成');
        }

        async function cleanupServiceWorkers() {
            log('开始清理ServiceWorker...');
            
            if (!('serviceWorker' in navigator)) {
                log('浏览器不支持ServiceWorker，无需清理');
                return;
            }

            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                
                if (registrations.length === 0) {
                    log('没有发现ServiceWorker，无需清理');
                    return;
                }

                log(`发现 ${registrations.length} 个ServiceWorker，开始清理...`);

                const results = await Promise.all(
                    registrations.map(async (registration) => {
                        try {
                            const success = await registration.unregister();
                            log(`清理ServiceWorker: ${registration.scope} - ${success ? '成功' : '失败'}`);
                            return success;
                        } catch (error) {
                            log(`清理ServiceWorker失败: ${registration.scope} - ${error.message}`);
                            return false;
                        }
                    })
                );

                const successCount = results.filter(r => r).length;
                log(`清理完成: ${successCount}/${registrations.length} 个ServiceWorker已清理`);

                if (successCount === registrations.length) {
                    alert('ServiceWorker清理完成！建议刷新页面以确保更改生效。');
                } else {
                    alert('部分ServiceWorker清理失败，请手动清理或联系技术支持。');
                }

                // 重新检查状态
                setTimeout(checkStatus, 1000);
            } catch (error) {
                log(`清理ServiceWorker时出错: ${error.message}`);
                alert(`清理失败: ${error.message}`);
            }
        }

        function showDetails() {
            const detailsDiv = document.getElementById('details');
            const detailsContent = document.getElementById('details-content');
            
            if (detailsDiv.style.display === 'none') {
                const details = {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                    serviceWorkerSupport: 'serviceWorker' in navigator,
                    mockSystemGlobal: !!window.MockSystem
                };
                
                detailsContent.textContent = JSON.stringify(details, null, 2);
                detailsDiv.style.display = 'block';
            } else {
                detailsDiv.style.display = 'none';
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
